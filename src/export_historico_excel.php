<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';
require_once __ROOT__ . '/src/classes/partidotorneo.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    // Get POST data
    $id_partido = isset($_POST['id_partido']) ? limpiar_datos($_POST['id_partido']) : '';
    $filter_type = isset($_POST['filter_type']) ? limpiar_datos($_POST['filter_type']) : 'todos';
    $partido_home = isset($_POST['partido_home']) ? limpiar_datos($_POST['partido_home']) : '';
    $partido_away = isset($_POST['partido_away']) ? limpiar_datos($_POST['partido_away']) : '';
    
    // Validate required parameters
    if (empty($id_partido) || empty($partido_home) || empty($partido_away)) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required parameters']);
        exit();
    }
    
    // Get partido data
    $cur_partido = Partido::get($id_partido, $conexion);
    if (empty($cur_partido->id)) {
        http_response_code(404);
        echo json_encode(['error' => 'Partido not found']);
        exit();
    }
    
    // Get partido torneos for additional countries
    $partido_torneos = PartidoTorneo::getList($id_partido, $conexion);
    
    // Get historico data
    $param = array();
    $param['home'] = $cur_partido->home;
    $param['away'] = $cur_partido->away;
    $param['paises_adicionales'] = $partido_torneos;
    $param['orderby_fecha'] = 1;
    $partidos_infos = PartidoInfo::getList($param, $conexion);
    
    // Filter data based on filter type
    $filtered_data = array();
    foreach ($partidos_infos as $partido_info) {
        $show_row = false;
        
        switch ($filter_type) {
            case 'todos':
                $show_row = true;
                break;
            case 'home':
                // Home: Show records where PartidoInfo.home OR PartidoInfo.away = Partido.home
                $show_row = ($partido_info->home === $cur_partido->home || $partido_info->away === $cur_partido->home);
                break;
            case 'home_h':
                // Home @H: Show records where PartidoInfo.home = Partido.home
                $show_row = ($partido_info->home === $cur_partido->home);
                break;
            case 'home_a':
                // Home @A: Show records where PartidoInfo.away = Partido.home
                $show_row = ($partido_info->away === $cur_partido->home);
                break;
            case 'away':
                // Away: Show records where PartidoInfo.away OR PartidoInfo.home = Partido.away
                $show_row = ($partido_info->away === $cur_partido->away || $partido_info->home === $cur_partido->away);
                break;
            case 'away_a':
                // Away @A: Show records where PartidoInfo.away = Partido.away
                $show_row = ($partido_info->away === $cur_partido->away);
                break;
            case 'away_h':
                // Away @H: Show records where PartidoInfo.home = Partido.away
                $show_row = ($partido_info->home === $cur_partido->away);
                break;
        }
        
        if ($show_row) {
            $filtered_data[] = $partido_info;
        }
    }
    
    // Generate Excel file using HTML table approach (Excel can read HTML tables as .xls)
    $filename = 'historico_' . $partido_home . '_vs_' . $partido_away . '_' . $filter_type . '.xls';

    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');

    // Create Excel content using HTML table structure
    generateExcelFile($filtered_data, $partido_home, $partido_away, $filter_type);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function generateExcelFile($data, $partido_home, $partido_away, $filter_type) {
    // Create Excel file using HTML table format (Excel can read this as .xls)
    echo '<!DOCTYPE html>';
    echo '<html>';
    echo '<head>';
    echo '<meta charset="UTF-8">';
    echo '<title>Historico - ' . htmlspecialchars($partido_home) . ' vs ' . htmlspecialchars($partido_away) . '</title>';
    echo '</head>';
    echo '<body>';

    // Add title
    echo '<h2>Historico - ' . htmlspecialchars($partido_home) . ' vs ' . htmlspecialchars($partido_away) . ' (' . htmlspecialchars($filter_type) . ')</h2>';

    // Create table
    echo '<table border="1" cellpadding="5" cellspacing="0">';

    // Add headers
    echo '<thead>';
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    $headers = ['#', 'Fecha', 'Days Ago', 'Home', 'Away', 'Torneo', 'Temporada', 'Home goals', 'Away goals'];
    foreach ($headers as $header) {
        echo '<th>' . htmlspecialchars($header) . '</th>';
    }
    echo '</tr>';
    echo '</thead>';

    // Add data rows
    echo '<tbody>';
    $row_number = 1;
    foreach ($data as $partido_info) {
        // Calculate days ago using Bogotá timezone
        setTimeZoneCol(); // Set Bogotá timezone
        $fecha_actual = create_date();
        $days_ago = '';
        if (!empty($partido_info->fecha)) {
            $days_diff = getDateDiffDaysNonLiteral($partido_info->fecha, $fecha_actual);
            $days_ago = abs($days_diff); // Always show positive number for "days ago"
        }

        echo '<tr>';
        echo '<td>' . $row_number . '</td>';
        echo '<td>' . htmlspecialchars($partido_info->fecha) . '</td>';
        echo '<td>' . $days_ago . '</td>';
        echo '<td>' . htmlspecialchars($partido_info->home) . '</td>';
        echo '<td>' . htmlspecialchars($partido_info->away) . '</td>';
        echo '<td>' . htmlspecialchars($partido_info->nom_pais) . '</td>';
        echo '<td>' . htmlspecialchars($partido_info->season) . '</td>';
        echo '<td>' . $partido_info->homegoals . '</td>';
        echo '<td>' . $partido_info->awaygoals . '</td>';
        echo '</tr>';

        $row_number++;
    }
    echo '</tbody>';
    echo '</table>';

    echo '</body>';
    echo '</html>';
}

?>
