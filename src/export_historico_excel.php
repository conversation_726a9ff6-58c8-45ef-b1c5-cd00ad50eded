<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';
require_once __ROOT__ . '/src/classes/partidotorneo.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Include PhpSpreadsheet
require_once __ROOT__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    // Get POST data
    $id_partido = isset($_POST['id_partido']) ? limpiar_datos($_POST['id_partido']) : '';
    $filter_type = isset($_POST['filter_type']) ? limpiar_datos($_POST['filter_type']) : 'todos';
    $partido_home = isset($_POST['partido_home']) ? limpiar_datos($_POST['partido_home']) : '';
    $partido_away = isset($_POST['partido_away']) ? limpiar_datos($_POST['partido_away']) : '';
    
    // Validate required parameters
    if (empty($id_partido) || empty($partido_home) || empty($partido_away)) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required parameters']);
        exit();
    }
    
    // Get partido data
    $cur_partido = Partido::get($id_partido, $conexion);
    if (empty($cur_partido->id)) {
        http_response_code(404);
        echo json_encode(['error' => 'Partido not found']);
        exit();
    }
    
    // Get partido torneos for additional countries
    $partido_torneos = PartidoTorneo::getList($id_partido, $conexion);
    
    // Get historico data
    $param = array();
    $param['home'] = $cur_partido->home;
    $param['away'] = $cur_partido->away;
    $param['paises_adicionales'] = $partido_torneos;
    $param['orderby_fecha'] = 1;
    $partidos_infos = PartidoInfo::getList($param, $conexion);
    
    // Filter data based on filter type
    $filtered_data = array();
    foreach ($partidos_infos as $partido_info) {
        $show_row = false;
        
        switch ($filter_type) {
            case 'todos':
                $show_row = true;
                break;
            case 'home':
                // Home: Show records where PartidoInfo.home OR PartidoInfo.away = Partido.home
                $show_row = ($partido_info->home === $cur_partido->home || $partido_info->away === $cur_partido->home);
                break;
            case 'home_h':
                // Home @H: Show records where PartidoInfo.home = Partido.home
                $show_row = ($partido_info->home === $cur_partido->home);
                break;
            case 'home_a':
                // Home @A: Show records where PartidoInfo.away = Partido.home
                $show_row = ($partido_info->away === $cur_partido->home);
                break;
            case 'away':
                // Away: Show records where PartidoInfo.away OR PartidoInfo.home = Partido.away
                $show_row = ($partido_info->away === $cur_partido->away || $partido_info->home === $cur_partido->away);
                break;
            case 'away_a':
                // Away @A: Show records where PartidoInfo.away = Partido.away
                $show_row = ($partido_info->away === $cur_partido->away);
                break;
            case 'away_h':
                // Away @H: Show records where PartidoInfo.home = Partido.away
                $show_row = ($partido_info->home === $cur_partido->away);
                break;
        }
        
        if ($show_row) {
            $filtered_data[] = $partido_info;
        }
    }
    
    // Generate Excel file using PhpSpreadsheet
    $filename = 'historico_' . $partido_home . '_vs_' . $partido_away . '_' . $filter_type . '.xlsx';

    // Create Excel file using PhpSpreadsheet
    generateExcelFile($filtered_data, $partido_home, $partido_away, $filter_type, $filename);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function generateExcelFile($data, $partido_home, $partido_away, $filter_type, $filename) {
    try {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set document properties
        $spreadsheet->getProperties()
            ->setCreator('MyDash System')
            ->setTitle('Historico - ' . $partido_home . ' vs ' . $partido_away)
            ->setSubject('Historical Match Data')
            ->setDescription('Historical match data export for ' . $partido_home . ' vs ' . $partido_away . ' (' . $filter_type . ')');

        // Set sheet title
        $sheet->setTitle('Historico');

        // Define headers
        $headers = ['#', 'Fecha', 'Days Ago', 'Home', 'Away', 'Torneo', 'Temporada', 'Home goals', 'Away goals'];

        // Add headers to first row
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $col++;
        }

        // Style the header row
        $headerRange = 'A1:I1';
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => '000000']
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E0E0E0']
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER
            ]
        ]);

        // Add data rows
        $row_number = 2; // Start from row 2 (after headers)
        $data_row_number = 1;

        foreach ($data as $partido_info) {
            // Calculate days ago using Bogotá timezone
            setTimeZoneCol(); // Set Bogotá timezone
            $fecha_actual = create_date();
            $days_ago = '';
            if (!empty($partido_info->fecha)) {
                $days_diff = getDateDiffDaysNonLiteral($partido_info->fecha, $fecha_actual);
                $days_ago = abs($days_diff); // Always show positive number for "days ago"
            }

            // Format date to yyyy-MM-dd format
            $formatted_date = '';
            if (!empty($partido_info->fecha)) {
                $formatted_date = convertToStandardDate($partido_info->fecha);
            }

            // Set cell values
            $sheet->setCellValue('A' . $row_number, $data_row_number);
            $sheet->setCellValue('B' . $row_number, $formatted_date);
            $sheet->setCellValue('C' . $row_number, $days_ago);
            $sheet->setCellValue('D' . $row_number, $partido_info->home);
            $sheet->setCellValue('E' . $row_number, $partido_info->away);
            $sheet->setCellValue('F' . $row_number, $partido_info->nom_pais);
            $sheet->setCellValue('G' . $row_number, $partido_info->season);
            $sheet->setCellValue('H' . $row_number, $partido_info->homegoals);
            $sheet->setCellValue('I' . $row_number, $partido_info->awaygoals);

            $row_number++;
            $data_row_number++;
        }

        // Auto-size columns
        foreach (range('A', 'I') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Apply borders to all data
        if ($row_number > 2) {
            $dataRange = 'A1:I' . ($row_number - 1);
            $sheet->getStyle($dataRange)->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000']
                    ]
                ]
            ]);
        }

        // Set proper headers for .xlsx download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        // Create writer and output file
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');

        // Clean up
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Error generating Excel file: ' . $e->getMessage()]);
    }
}

?>
